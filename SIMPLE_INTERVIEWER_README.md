# Simple React Interviewer Agent

A simple React-style agent that gathers basic workflow requirements from users through conversational questions and interrupts.

## Overview

This is a streamlined interviewer agent that:
- Uses a simple React pattern (Reason → Act → Observe loop)
- Asks questions via LangGraph interrupts
- Focuses only on basic requirements (purpose and inputs)
- Hands off to the Planner Agent for technical details

## Architecture

```
User Prompt → Simple React Agent → [Interrupt for Questions] → Requirements Summary
```

### Key Components

1. **Single Agent Node**: `interviewer_agent()` - handles all logic
2. **Simple Loop**: Agent can loop back to ask more questions
3. **Interrupts**: Uses LangGraph interrupts to ask user questions
4. **Minimal State**: Only tracks basic requirements

## What It Does

### ✅ Focuses On:
- **Workflow Purpose**: What should this workflow accomplish?
- **Input Data**: What data will be provided and in what format?
- **Basic Context**: Essential details for understanding the use case

### ❌ Doesn't Handle:
- Processing logic (left to Planner Agent)
- Output requirements (left to Planner Agent)
- Integration details (left to Planner Agent)
- Technical constraints (left to Planner Agent)

## Files Created/Modified

```
agent_prompt/
  └── interviewer_agent.py          # Simple agent prompt

graph/
  └── interviewer_agent_graph.py    # React agent with interrupts

models/
  └── state.py                      # Added interviewer fields

utils/
  └── tool_schema.py               # Added interviewer schemas

test_simple_interviewer.py          # Test script
SIMPLE_INTERVIEWER_README.md        # This file
```

## State Fields Added

```python
# Interviewer agent specific fields
user_prompt: Optional[str] = None
workflow_requirements: Optional[Dict[str, Any]] = None
missing_information: List[str] = []
clarification_questions: List[str] = []
interview_complete: bool = False
interviewer_messages: Annotated[List[AnyMessage], add_messages] = []
```

## How It Works

1. **User provides initial prompt** (may be vague)
2. **Agent analyzes** if more information is needed
3. **If unclear**: Agent asks questions via interrupt
4. **User responds**: Response is added to conversation
5. **Loop continues** until agent has enough basic info
6. **Agent summarizes** requirements and marks complete
7. **Hand off** to Planner Agent for detailed planning

## Example Flow

```
User: "I want to process some data"

Agent: "I need more details to help you. What specific processing do you want to do with the data?"

[INTERRUPT - waits for user response]

User: "I want to validate email addresses in a CSV file"

Agent: "Great! What format is your CSV file in, and where will you get it from?"

[INTERRUPT - waits for user response]

User: "It's a standard CSV with customer info including emails, from our CRM export"

Agent: "Perfect! I now understand your requirements:
- Purpose: Validate email addresses in customer data
- Input: CSV file from CRM export with customer information
Ready to hand off to planning phase."

[COMPLETE]
```

## Testing

```bash
# Run automated tests
python test_simple_interviewer.py

# Run interactive test
python test_simple_interviewer.py --interactive
```

## Integration

The interviewer agent integrates with the existing workflow system:

```python
from graph.interviewer_agent_graph import create_interviewer_graph

# Create graph
interviewer_graph = create_interviewer_graph()

# Run with initial state
initial_state = State(
    workflow_graph=WorkflowGraph(),
    user_prompt="Your workflow request here"
)

# Execute (handles interrupts automatically)
result = interviewer_graph.invoke(initial_state, config)

# Check if complete
if result.interview_complete:
    # Hand off to planner with result.workflow_requirements
    pass
```

## Key Benefits

1. **Simple**: Single agent node with clear logic
2. **Interactive**: Uses interrupts for real user interaction
3. **Focused**: Only gathers essential information
4. **Efficient**: Doesn't over-interview users
5. **Integrated**: Works with existing workflow system
