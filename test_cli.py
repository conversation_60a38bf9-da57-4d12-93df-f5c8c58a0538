#!/usr/bin/env python3
"""
CLI program to test the interviewer_agent functionality.
<PERSON>les interrupts by prompting user for input and continuing execution.
"""

import sys

from langgraph.checkpoint.memory import MemorySaver

from graph.interviewer_agent_graph import create_interviewer_graph
from models.state import State
from models.workflowGraph import WorkflowGraph


def print_banner():
    """Print a welcome banner."""
    print("=" * 60)
    print("  Interviewer Agent CLI")
    print("=" * 60)
    print()


def get_example_prompt() -> str:
    """Return an example user prompt for workflow generation."""
    return "I want to create a workflow that processes customer feedback from multiple sources (email, social media, surveys) and generates automated responses and sentiment analysis reports."


def run_graph(user_prompt: str):
    """Run the interviewer agent graph with the given user prompt, handling interrupts."""
    print("\n" + "=" * 60)
    print(f"User Prompt: {user_prompt}")
    print("=" * 60)

    # Create checkpointer for state persistence
    checkpointer = MemorySaver()

    # Create the graph
    graph = create_interviewer_graph(checkpointer=checkpointer)

    # Initial state
    config = {"configurable": {"thread_id": "interviewer-thread-1"}}
    initial_workflow = WorkflowGraph(nodes={}, edges={})
    initial_workflow.add_node(
        node_id="start-node",
        label="Start",
        OriginalType="StartNode",
        type="component",
        position=(0, 0),
        parameters={},
    )

    initial_state = State(
        user_prompt=user_prompt,
        workflow_requirements={},
        missing_information=[],
        clarification_questions=[],
        interview_complete=False,
        interviewer_messages=[],
        workflow_graph=initial_workflow,
        nodes_to_test=[],
        mcps_infos={},
        verified_mcp_data={},
        dummy_data="",
        unauthorized_mcps=[],
        output="",
        main_agent_messages=[],
        planner_messages=[],
        workflow_generation_messages=[],
        task_based_generation_messages=[],
    )

    print("\nStarting graph execution...")
    print("-" * 60)

    # Execute the interviewer graph
    try:
        output = graph.invoke(initial_state, config=config)

        print("\n" + "=" * 60)
        print("INTERVIEWER AGENT RESULTS:")
        print("=" * 60)

        # Print interview completion status
        if output.get("interview_complete"):
            print("✅ Interview completed successfully!")
        else:
            print("⚠️  Interview not completed")

        # Print gathered requirements
        if output.get("workflow_requirements"):
            print("\n📋 GATHERED REQUIREMENTS:")
            print("-" * 40)
            requirements = output["workflow_requirements"]
            for category, details in requirements.items():
                if details:
                    print(f"\n{category.replace('_', ' ').title()}:")
                    if isinstance(details, dict):
                        for key, value in details.items():
                            print(f"  • {key}: {value}")
                    elif isinstance(details, list):
                        for item in details:
                            print(f"  • {item}")
                    else:
                        print(f"  {details}")

        # Print final requirement summary
        if output.get("requirement"):
            print(f"\n📝 REQUIREMENT SUMMARY:")
            print("-" * 40)
            print(output["requirement"])

        # Print any missing information
        if output.get("missing_information"):
            print(f"\n❓ MISSING INFORMATION:")
            print("-" * 40)
            for item in output["missing_information"]:
                print(f"  • {item}")

        # Print questions asked during interview
        if output.get("clarification_questions"):
            print(f"\n❓ QUESTIONS ASKED:")
            print("-" * 40)
            for i, question in enumerate(output["clarification_questions"], 1):
                print(f"  {i}. {question}")

    except Exception as e:
        print(f"\n[ERROR] Graph execution failed: {e}")
        import traceback

        traceback.print_exc()


def main():
    """Main CLI function."""
    print_banner()

    print("Enter your workflow requirements or idea:")
    print("\nExample:")
    print(f'  "{get_example_prompt()}"')
    print("\nOr press Enter to use the example above:")

    user_input = input().strip()

    if user_input == "":
        user_prompt = get_example_prompt()
        print("\nUsing example prompt")
    else:
        user_prompt = user_input

    run_graph(user_prompt)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user. Goodbye!")
        sys.exit(0)
