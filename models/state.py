import operator
from operator import add, or_
from typing import Annotated, List, Literal, Optional, Dict, Any

from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field

from models.workflowGraph import WorkflowGraph
from utils.reducer import todo_reducer


class State(BaseModel):
    main_agent_messages: Annotated[List[AnyMessage], add_messages]
    planner_messages: Annotated[List[AnyMessage], add_messages]
    workflow_generation_messages: Annotated[List[AnyMessage], add_messages]
    task_based_generation_messages: Annotated[List[AnyMessage], add_messages]

    plan: Optional[str] = None
    feedback: Optional[str] = None

    workflow_graph: WorkflowGraph
    task_workflow_graph: WorkflowGraph
    rag_message_ids: Annotated[List[str], add] = []
    get_context_message_ids: Annotated[List[str], add] = []

    stage: Literal["main", "planning", "workflow_generation", "end"] = "main"
    todo: Annotated[List[Dict[str, Any]], todo_reducer] = []


class testNodeState(BaseModel):
    test_node_messages: Annotated[List[AnyMessage], add_messages]
    nodes_to_test: Annotated[List[Dict], add] = []
    mcps_infos: Annotated[Dict, or_] = {}
    verified_mcp_data: Annotated[Dict[str, Dict], or_] = {}
    dummy_data: str = ""
    unauthorized_mcps: List[Dict[str, Any]] = []
    output: str = ""

