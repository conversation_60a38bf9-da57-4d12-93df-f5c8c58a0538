#!/usr/bin/env python3
"""
Test script for the Simple React Interviewer Agent.
This demonstrates a simple React agent that asks questions via interrupts.
"""

import os
import json
from models.state import State
from models.workflowGraph import WorkflowGraph
from graph.interviewer_agent_graph import create_interviewer_graph

def test_simple_interviewer():
    """Test the simple React interviewer agent with interrupts."""
    
    # Set up environment
    if not os.getenv("REQUESTY_API_KEY"):
        print("Please set REQUESTY_API_KEY environment variable")
        return
    
    print("=== SIMPLE REACT INTERVIEWER AGENT TEST ===")
    print()
    
    # Test with different prompts
    test_prompts = [
        "I want to process some data",  # Vague - should ask questions
        "I want to create a workflow that reads a CSV file with customer data and sends welcome emails to new customers",  # Clear - should summarize
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"Test {i}: '{prompt}'")
        print("-" * 60)
        
        # Create initial state
        initial_state = State(
            workflow_graph=WorkflowGraph(),
            user_prompt=prompt,
        )
        
        # Create interviewer graph
        interviewer_graph = create_interviewer_graph()
        
        try:
            # Run the agent
            config = {"configurable": {"thread_id": f"test-{i}"}}
            result = interviewer_graph.invoke(initial_state, config)
            
            print(f"Interview complete: {result.get('interview_complete', False)}")
            
            if result.get('workflow_requirements'):
                print("Requirements gathered:")
                print(json.dumps(result['workflow_requirements'], indent=2))
            
            # Show conversation
            if result.get('interviewer_messages'):
                print("\nConversation:")
                for j, msg in enumerate(result['interviewer_messages']):
                    if hasattr(msg, 'content') and msg.content.strip():
                        role = "System" if j == 0 else ("Agent" if "?" in msg.content else "Analysis")
                        print(f"  {role}: {msg.content[:100]}...")
            
        except Exception as e:
            print(f"Error: {str(e)}")
        
        print("\n" + "="*60 + "\n")

def interactive_test():
    """Interactive test where you can chat with the interviewer."""
    
    if not os.getenv("REQUESTY_API_KEY"):
        print("Please set REQUESTY_API_KEY environment variable")
        return
    
    print("=== INTERACTIVE INTERVIEWER TEST ===")
    print("Enter your workflow request, and the agent will ask clarifying questions.")
    print("Type 'quit' to exit.\n")
    
    user_prompt = input("Your workflow request: ").strip()
    if not user_prompt or user_prompt.lower() == 'quit':
        return
    
    # Create initial state
    initial_state = State(
        workflow_graph=WorkflowGraph(),
        user_prompt=user_prompt,
    )
    
    # Create interviewer graph
    interviewer_graph = create_interviewer_graph()
    
    try:
        config = {"configurable": {"thread_id": "interactive"}}
        
        # This would normally handle interrupts in a real application
        # For now, just run once to see the agent's response
        result = interviewer_graph.invoke(initial_state, config)
        
        print(f"\nAgent response:")
        if result.get('interviewer_messages'):
            last_msg = result['interviewer_messages'][-1]
            if hasattr(last_msg, 'content'):
                print(last_msg.content)
        
        if result.get('interview_complete'):
            print("\nInterview completed!")
            if result.get('workflow_requirements'):
                print("Final requirements:")
                print(json.dumps(result['workflow_requirements'], indent=2))
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        test_simple_interviewer()
