#!/usr/bin/env python3
"""
Example showing how the Interviewer Agent integrates with the workflow generation system.

The flow is:
1. User provides initial prompt
2. Interviewer Agent asks basic questions about purpose and inputs
3. Once basic requirements are gathered, hand off to Planner Agent
4. Planner Agent determines processing logic, outputs, integrations, etc.
5. Workflow Generation Agent creates the actual workflow
"""

import os
import json
from models.state import State
from models.workflowGraph import WorkflowGraph
from graph.interviewer_agent_graph import create_interviewer_graph

def demonstrate_interviewer_flow():
    """Demonstrate the interviewer agent's role in the workflow generation process."""
    
    print("=== INTERVIEWER AGENT INTEGRATION EXAMPLE ===")
    print()
    
    # Example user prompts that would benefit from interviewer questions
    example_prompts = [
        "I want to process some data",
        "Create a workflow for customer onboarding", 
        "I need to automate email sending",
        "Help me build a data processing pipeline",
        "I want to create a workflow that processes customer data and sends welcome emails"
    ]
    
    for i, prompt in enumerate(example_prompts, 1):
        print(f"Example {i}: '{prompt}'")
        
        # Analyze what the interviewer would need to ask
        analysis = analyze_prompt_gaps(prompt)
        print(f"  Missing info: {', '.join(analysis['missing'])}")
        print(f"  Questions needed: {analysis['questions_needed']}")
        print(f"  Example questions: {analysis['example_questions']}")
        print()

def analyze_prompt_gaps(prompt):
    """Analyze what information is missing from a user prompt."""
    
    # Check for workflow purpose clarity
    purpose_clear = any(word in prompt.lower() for word in [
        'process', 'send', 'create', 'generate', 'analyze', 'transform', 'validate'
    ])
    
    # Check for input information
    has_input_info = any(word in prompt.lower() for word in [
        'csv', 'file', 'database', 'api', 'data', 'email', 'customers', 'users'
    ])
    
    missing = []
    example_questions = []
    
    if not purpose_clear:
        missing.append("workflow purpose")
        example_questions.append("What exactly do you want this workflow to accomplish?")
    
    if not has_input_info:
        missing.append("input data")
        example_questions.append("What data or information will you be providing to this workflow?")
        example_questions.append("Where will the input data come from? (files, database, API, etc.)")
    
    # Check if prompt is too vague
    if len(prompt.split()) < 5:
        missing.append("context")
        example_questions.append("Can you provide more details about your use case?")
    
    return {
        "missing": missing,
        "questions_needed": len(missing) > 0,
        "example_questions": example_questions[:2]  # Limit to 2 questions
    }

def simulate_interview_conversation():
    """Simulate a conversation between user and interviewer agent."""
    
    print("=== SIMULATED INTERVIEW CONVERSATION ===")
    print()
    
    # Initial user prompt
    user_prompt = "I want to process some customer data"
    print(f"User: {user_prompt}")
    print()
    
    # Interviewer analysis
    print("Interviewer Agent (thinking): This prompt is too vague. I need to ask about:")
    print("- What specific processing should be done?")
    print("- What customer data will be provided?")
    print("- Where does the data come from?")
    print()
    
    # Interviewer questions
    print("Interviewer Agent: I'd like to understand your workflow better. Can you tell me:")
    print("1. What specific processing do you want to do with the customer data?")
    print("2. What format is your customer data in? (CSV file, database, API, etc.)")
    print()
    
    # User response
    print("User: I have a CSV file with customer information and I want to send welcome emails to new customers.")
    print()
    
    # Interviewer follow-up
    print("Interviewer Agent: Great! Just to clarify:")
    print("1. What information is in your CSV file? (names, emails, signup dates, etc.)")
    print("2. How do you identify which customers are 'new'?")
    print()
    
    # User final response
    print("User: The CSV has customer names, email addresses, and signup dates. New customers are those who signed up in the last 7 days.")
    print()
    
    # Interviewer completion
    print("Interviewer Agent: Perfect! I now have the basic requirements:")
    print("- Purpose: Send welcome emails to new customers")
    print("- Input: CSV file with customer names, emails, and signup dates")
    print("- Context: New customers = signed up in last 7 days")
    print()
    print("Handing off to Planner Agent to determine:")
    print("- How to read and parse the CSV file")
    print("- How to filter customers by signup date")
    print("- How to compose and send welcome emails")
    print("- What email service to use")
    print("- Error handling for invalid emails")
    print("- Output format and logging")

if __name__ == "__main__":
    demonstrate_interviewer_flow()
    print("\n" + "="*60 + "\n")
    simulate_interview_conversation()
