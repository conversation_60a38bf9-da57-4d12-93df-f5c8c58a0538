# Interviewer Agent

The Interviewer Agent is responsible for gathering basic requirements from users before workflow generation begins. It focuses on understanding **what** the user wants to accomplish and **what inputs** they have available, leaving the technical details to the Planner Agent.

## Purpose

The Interviewer Agent serves as the first step in the workflow generation process, ensuring that we have enough basic information to create a meaningful workflow plan. It keeps things simple and focused, avoiding technical complexity.

## What the Interviewer Agent Does

### ✅ Asks About:
- **Workflow Purpose**: What should this workflow accomplish?
- **Input Data**: What data/information will be provided?
- **Basic Context**: Important details about the use case

### ❌ Does NOT Ask About:
- Processing logic (handled by Planner Agent)
- Output requirements (handled by Planner Agent)  
- Integration needs (handled by Planner Agent)
- Constraints and limitations (handled by Planner Agent)
- Error handling (handled by Planner Agent)
- Performance requirements (handled by Planner Agent)

## Integration with Existing System

The Interviewer Agent fits into the workflow generation pipeline as follows:

```
User Prompt → Interviewer Agent → Planner Agent → Workflow Generation Agent
```

1. **User provides initial prompt** (may be vague or incomplete)
2. **Interviewer Agent** asks basic clarifying questions
3. **Planner Agent** receives clear requirements and creates detailed plan
4. **Workflow Generation Agent** builds the actual workflow

## State Management

The Interviewer Agent adds these fields to the State model:

```python
# Interviewer agent specific fields
user_prompt: Optional[str] = None
workflow_requirements: Optional[Dict[str, Any]] = None
missing_information: List[str] = []
clarification_questions: List[str] = []
interview_complete: bool = False
```

## Available Tools

The Interviewer Agent has three tools:

1. **analyze_requirements**: Check if enough basic information has been gathered
2. **ask_clarification**: Ask simple, focused questions to fill gaps
3. **finalize_requirements**: Complete the interview with basic requirements

## Example Usage

### Basic Test
```bash
python test_interviewer_agent.py
```

### Interactive Test
```bash
python test_interviewer_agent.py --interactive
```

### Integration Example
```bash
python example_interviewer_integration.py
```

## Example Conversations

### Vague Prompt
**User**: "I want to process some data"

**Interviewer**: 
1. What specific processing do you want to do with the data?
2. What type of data will you be working with?

### Clear Prompt
**User**: "I want to create a workflow that reads customer data from a CSV file and sends welcome emails to new customers"

**Interviewer**: This looks clear! Just to confirm:
1. What information is in your CSV file?
2. How do you identify which customers are 'new'?

## Files Structure

```
agent_prompt/
  └── interviewer_agent.py          # Agent prompt and instructions

graph/
  └── interviewer_agent_graph.py    # LangGraph implementation

models/
  └── state.py                      # Updated with interviewer fields

utils/
  └── tool_schema.py               # Tool schemas for interviewer

test_interviewer_agent.py           # Test script
example_interviewer_integration.py  # Integration example
```

## Key Design Principles

1. **Keep it Simple**: Only ask about basics, not technical details
2. **Be Efficient**: Don't over-interview, get essentials and move on
3. **Trust the Planner**: Let the Planner Agent handle technical complexity
4. **Focus on Clarity**: Ensure user intent is clear before planning begins
