import json
import os

from langchain_core.messages import ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

from models.state import State
from utils.RAG import RAG_search
from utils.todo import get_todos, add_todo, delete_todo
from utils.tool_schema import (
    DELETE_TODO_SCHEMA,
    GET_CURRENT_WORKFLOW_SCHEMA,
    RAG_TOOL_SCHEMA,
    GET_TODOS_SCHEMA,
    ADD_TODO_SCHEMA,
)


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        client_args={
            "api_key": REQUESTY_API_KEY,
            "base_url": "https://router.requesty.ai/v1",
        },
        model_id="anthropic/claude-sonnet-4",
    )
    llm = llm.bind_tools(
        [
            RAG_TOOL_SCHEMA,
            GET_CURRENT_WORKFLOW_SCHEMA,
            GET_TODOS_SCHEMA,
            ADD_TODO_SCHEMA,
            DELETE_TODO_SCHEMA,
        ]
    )
    return llm


def agent_node(state: State) -> State:
    update = {"planner_messages": []}
    llm = get_llm()
    response = llm.invoke(state.planner_messages)
    update["planner_messages"].append(response)
    if not response.tool_calls:
        update["plan"] = response.content
        update["stage"] = "main"
    return update


def tools_node(state: State) -> State:
    update = {"planner_messages": [], "todo": []}
    tool_calls = state.planner_messages[-1].tool_calls
    for tool_call in tool_calls:
        if tool_call["name"] == "RAG_search":
            result = RAG_search(tool_call["args"]["query"])
            for i in range(len(result)):
                result[i].pop("updated_at")
                result[i].pop("logo")
            update["planner_messages"].append(
                ToolMessage(content=json.dumps(result), tool_call_id=tool_call["id"])
            )
        if tool_call["name"] == "get_current_workflow":
            update["planner_messages"].append(
                ToolMessage(
                    content=json.dumps(state.workflow_graph.get_graph_repr()),
                    tool_call_id=tool_call["id"],
                )
            )
        if tool_call["name"] == "get_todos":
            todos = get_todos(state)
            update["planner_messages"].append(
                ToolMessage(content=json.dumps(todos), tool_call_id=tool_call["id"])
            )
        if tool_call["name"] == "add_todo":
            todo = add_todo(
                tool_call["args"]["task_key"],
                tool_call["args"]["title"],
                tool_call["args"]["details"],
                tool_call["args"]["plan"],
            )
            update["todo"].append(todo)
            update["planner_messages"].append(
                ToolMessage(content="Todo added successfully.", tool_call_id=tool_call["id"])
            )
        if tool_call["name"] == "delete_todo":
            todo = delete_todo(tool_call["args"]["todo"])
            update["todo"].append(todo)
            update["planner_messages"].append(
                ToolMessage(content="Todo deleted successfully.", tool_call_id=tool_call["id"])
            )
    return update


def router(state: State):
    tool_calls = state.planner_messages[-1].tool_calls
    if tool_calls:
        return "tools"
    else:
        return "end"


def create_planner_graph():
    planner_graph = StateGraph(State)
    planner_graph.add_node("agent", agent_node)
    planner_graph.add_node("tools", tools_node)
    planner_graph.add_conditional_edges("agent", router, {"tools": "tools", "end": END})
    planner_graph.add_edge("tools", "agent")
    planner_graph.set_entry_point("agent")
    return planner_graph.compile()
