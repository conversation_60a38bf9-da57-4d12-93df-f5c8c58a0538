import json
import os

from langchain_core.messages import HumanMessage, ToolMessage
from langchain_openai import Chat<PERSON>penA<PERSON>
from langgraph.graph import END, StateGraph
from langgraph.types import interrupt
from graph.planner_agent_graph import create_planner_graph
from graph.workflow_generation_graph import create_workflow_generation_graph
from models.state import State
from utils.tool_schema import (
    ASK_USER_SCHEMA,
    GET_CURRENT_WORKFLOW_SCHEMA,
    PLANNER_AGENT_SCHEMA,
    WORKFLOW_GENERATION_SCHEMA,
)


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        client_args={
            "api_key": REQUESTY_API_KEY,
            "base_url": "https://router.requesty.ai/v1",
        },
        model_id="anthropic/claude-sonnet-4",
    )
    llm = llm.bind_tools(
        [
            PLANNER_AGENT_SCHEMA,
            WORK<PERSON>OW_GENERATION_SCHEMA,
            GET_CURRENT_WORKFLOW_SCHEMA,
            ASK_USER_SCHEMA,
        ]
    )
    return llm


def agent_node(state: State):
    update = {"main_agent_messages": []}
    llm = get_llm()
    response = llm.invoke(state.main_agent_messages)
    update["main_agent_messages"].append(response)
    if not response.tool_calls:
        update["stage"] = "end"
    else:
        tool_call = response.tool_calls[0]
        if tool_call["name"] == "planner":
            update["stage"] = "planning"
            prompt = tool_call["args"]["prompt"]
            planner_input = f"""
            prompt: {prompt}
            previous_plan: {state.plan}
            workflow: {json.dumps(state.workflow_graph.get_graph_repr())}
            """
            update["planner_messages"] = [HumanMessage(content=planner_input)]
        elif tool_call["name"] == "workflow_generation":
            update["stage"] = "workflow_generation"
            prompt = tool_call["args"]["prompt"]
            workflow_generation_input = f"""
            prompt: {prompt}
            plan: {state.plan}
            workflow: {json.dumps(state.workflow_graph.get_graph_repr())}
            """
            update["workflow_generation_messages"] = [
                HumanMessage(content=workflow_generation_input)
            ]
        elif tool_call["name"] == "ask_user":
            pass
    return update


def get_current_workflow(state: State):
    update = {"main_agent_messages": []}
    tool_call = state.main_agent_messages[-1].tool_calls[0]
    update["main_agent_messages"].append(
        ToolMessage(
            content=json.dumps(state.workflow_graph.get_graph_repr()),
            tool_call_id=tool_call["id"],
        )
    )
    return update

def ask_user(state: State):
    update = {"main_agent_messages": []}
    input_ = interrupt(state.main_agent_messages[-1].tool_calls[0]["args"]["question"])
    update["main_agent_messages"].append(
        ToolMessage(
            content=input_,
            tool_call_id=state.main_agent_messages[-1].tool_calls[0]["id"],
        )
    )
    return update

def router(state: State):
    if state.stage == "planning":
        return "planner_agent"
    elif state.stage == "workflow_generation":
        return "workflow_generation_agent"
    elif state.stage == "end":
        return "end"
    elif state.main_agent_messages[-1].tool_calls[0]["name"] == "get_current_workflow":
        return "get_current_workflow"
    else:
        return "ask_user"


def create_main_agent_graph():
    planner_agent = create_planner_graph()
    workflow_generation_agent = create_workflow_generation_graph()

    main_agent_graph = StateGraph(State)
    main_agent_graph.add_node("agent", agent_node)
    main_agent_graph.add_node("planner_agent", planner_agent)
    main_agent_graph.add_node("workflow_generation_agent", workflow_generation_agent)
    main_agent_graph.add_node("get_current_workflow", get_current_workflow)
    main_agent_graph.add_conditional_edges(
        "agent",
        router,
        {
            "planner_agent": "planner_agent",
            "workflow_generation_agent": "workflow_generation_agent",
            "get_current_workflow": "get_current_workflow",
            "end": END,
        },
    )
    main_agent_graph.add_edge("planner_agent", "agent")
    main_agent_graph.add_edge("workflow_generation_agent", "agent")
    main_agent_graph.add_edge("get_current_workflow", "agent")
    main_agent_graph.set_entry_point("agent")
    return main_agent_graph.compile()
