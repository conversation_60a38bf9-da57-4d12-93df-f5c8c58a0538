import json
import os
from typing import Any, Dict

from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.types import interrupt

from agent_prompt.interviewer_agent import INTERVIEWER_AGENT_PROMPT
from models.state import State

REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")

ask_user_schema = {
    "name": "ask_user",
    "description": "Ask the user a question",
    "parameters": {
        "type": "object",
        "properties": {
            "question": {"type": "string", "description": "The question to ask the user."}
        },
        "required": ["question"],
    },
}

def get_llm():
    return ChatOpenAI(
        base_url="https://router.requesty.ai/v1",
        model="anthropic/claude-sonnet-4",
        api_key=REQUESTY_API_KEY,
    ).bind_tools([ask_user_schema])


def interviewer_agent(state: State):
    """Simple React agent that asks questions and gathers requirements."""
    print("\n=== INTERVIEWER AGENT ===")

    # Initialize messages if empty
    update = {"interviewer_messages": []}
    if not state.interviewer_messages:
        system_message = SystemMessage(content=INTERVIEWER_AGENT_PROMPT)

        # Create initial context
        context = f"""
        User's initial request: {state.user_prompt or "No request provided"}

        Analyze this request and determine if you need more information to understand:
        1. What the workflow should do (purpose)
        2. What inputs will be provided

        If information is missing, ask simple clarifying questions.
        If you have enough basic information, summarize the requirements.
        """

        human_message = HumanMessage(content=context)
        messages = [system_message, human_message]
        update["interviewer_messages"].extend(messages)
    else:
        messages = state.interviewer_messages

    # Get LLM response
    llm = get_llm()
    response = llm.invoke(messages)
    update["interviewer_messages"].append(response)
    return update

def ask_user(state: State):
    update = {"interviewer_messages": []}
    input_ = interrupt(state.interviewer_messages[-1].tool_calls[0]["args"]["question"])
    update["interviewer_messages"].append(
        ToolMessage(
            content=input_,
            tool_call_id=state.interviewer_messages[-1].tool_calls[0]["id"],
        )
    )
    return update

def router(state: State):
    if state.interviewer_messages[-1].tool_calls:
        return "ask_user"
    else:
        return "end"


def create_interviewer_graph(checkpointer=None):
    """Create a simple interviewer agent graph with interrupts."""
    print("\n=== CREATING SIMPLE INTERVIEWER GRAPH ===")

    interviewer_graph = StateGraph(State)

    # Add single agent node
    interviewer_graph.add_node("interviewer", interviewer_agent)
    interviewer_graph.add_node("ask_user", ask_user)

    # Add conditional edge to continue or end
    interviewer_graph.add_conditional_edges(
        "interviewer",
        router,
        {
            "ask_user": "ask_user",
            "end": END,
        },
    )

    interviewer_graph.add_edge("ask_user", "interviewer")

    # Set entry point
    interviewer_graph.set_entry_point("interviewer")

    print("Simple interviewer graph created successfully")
    return interviewer_graph.compile(
        checkpointer=checkpointer
    )
